"use client";
import React, { useState } from "react";
import Image from "next/image";
import externalPageImage from "../../../public/telex-footer-500.svg";
import Link from "next/link";
import { Plus_Jakarta_Sans } from "next/font/google";
import instagramIcon from "../../../public/instagram-fill.svg";
import tiktokIcon from "../../../public/tiktok-fill.svg";
import facebookIcon from "../../../public/facebook-fill.svg";
import { PostRequest } from "~/utils/request";
import { useToast } from "~/components/ui/use-toast";
import { Loader2 } from "lucide-react";

const productLinks = [
  {
    id: 0,
    label: "Customer Support with AI",
    href: "/products/customer-support",
  },
  {
    id: 1,
    label: "Sales With AI",
    href: "/products/sales-ai",
  },
  {
    id: 2,
    label: "Devops and Site Reliability",
    href: "/products/devops-and-site-reliability",
  },
  {
    id: 3,
    label: "Marketing",
    href: "/products/marketing",
  },
  {
    id: 4,
    label: "Document Creation and Management",
    href: "/products/document-creation-and-management",
  },
  {
    id: 5,
    label: "IT Operations",
    href: "/products/it-operations",
  },
  {
    id: 6,
    label: "Video Generation",
    href: "/products/video-generation",
  },
  {
    id: 7,
    label: "Data Extraction and Analysis",
    href: "/products/data-extraction-and-analysis",
  },
  {
    id: 8,
    label: "Customer Relationship",
    href: "/products/customer-relationship",
  },
  {
    id: 9,
    label: "Lead Generation Nuturing",
    href: "/products/lead-generation-nuturing",
  },
  {
    id: 10,
    label: "Site Security",
    href: "/products/site-security",
  },
  {
    id: 11,
    label: "Content Creation",
    href: "/products/content-creation",
  },
  {
    id: 12,
    label: "Market Research",
    href: "/products/market-research",
  },
  {
    id: 13,
    label: "Document Analysis",
    href: "/products/document-analysis",
  },
  {
    id: 14,
    label: "Invoice Processing",
    href: "/products/invoice-processing",
  },
];

const supportLinks = [
  {
    id: 0,
    label: "Pricing",
    href: "/pricing",
  },
  {
    id: 1,
    label: "Help Center",
    href: "/help",
  },
  {
    id: 2,
    label: "FAQ",
    href: "/faq",
  },
  {
    id: 3,
    label: "Contact Us",
    href: "/contact",
  },
  // {
  //   id: 4,
  //   label: "Waitlist",
  //   href: "/contact",
  // },
  // {
  //   id: 5,
  //   label: "How Telex works",
  //   href: "/how-telex-works",
  // },
  // {
  //   id: 6,
  //   label: "About Us",
  //   href: "/about-us",
  // },
];

const resourcesLinks = [
  { id: 0, label: "Guides", href: "/resources" },
  // { id: 1, label: "Blogs", href: "/resources" },
  { id: 2, label: "Privacy Policy", href: "/policy" },
  { id: 3, label: "Terms of Service", href: "/terms-of-service" },
];

const plusJakartaSans = Plus_Jakarta_Sans({ subsets: ["latin"] });

const ExternalPageFooter = () => {
  const [email, setEmail] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState<boolean>(false);
  const { toast } = useToast();

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    setLoading(true);
    const token = localStorage.getItem("token") || "";

    if (!validateEmail(email)) {
      setError("Please enter a valid email address.");
      setLoading(false);
      return;
    }

    const req = await PostRequest("/newsletter", { email: email }, token);
    setLoading(false);
    if (req?.data?.status_code === 201) {
      setEmail("");
      setError("");
      toast({
        description: "Email sent successfully!",
      });
    } else {
      setError(req?.data?.message);
      return;
    }
  };

  const validateEmail = (email: string) => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(String(email).toLowerCase());
  };

  //

  return (
    <footer className=" bg-[#FAFAFF]  flex flex-col text-[#fafafa] ">
      <div className="w-full flex flex-col xl:max-w-[1550px] lg:mx-auto ">
        <div className=" w-full flex flex-col xl:flex-row justify-between  gap-y-4 sm:gap-y-2 lg:gap-y-0 xl:gap-y-20 lg:flex-nowrap">
          <div className="basis-1/4 pr-2  space-y-4 px-6 py-4 xl:px-16 sm:pt-20">
            <Link href={"/"} className="hidden sm:hidden md:block">
              <Image src={externalPageImage} alt="Telex logo" />
            </Link>
            <form
              onSubmit={handleSubmit}
              className="flex flex-col gap-4 w-full sm:w-fit md:basis-1/6"
            >
              <label className="text-lg font-semibold text-black">
                Sign up to our Newsletter
              </label>
              <div className="">
                <div className="flex justify-between items-center w-full h-14 p-2 self-stretch bg-white rounded-2xl sm:w-[420px] gap-2 lg:gap-0 border">
                  <input
                    type="email"
                    name="newsletter"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className={`${plusJakartaSans.className} min-w-28 w-44 sm:w-64 h-full p-2 bg-transparent placeholder:text-[#a0a0a0] text-black focus:outline-none text-sm sm:text-base xl:text-lg`}
                  />
                  <button
                    type="submit"
                    className="px-4 py-2 h-full flex items-center justify-center rounded-[8px]  text-sm font-medium leading-5 bg-primary-500 hover:bg-opacity-80 transition-all"
                  >
                    <p className={loading ? "opacity-0" : ""}>Subscribe</p>
                    {loading ? (
                      <Loader2 className="animate-spin absolute" />
                    ) : null}
                  </button>
                </div>
                {error && <p className="text-red-500 text-sm mt-2">{error}</p>}
              </div>
            </form>
          </div>

          <div className="w-full flex flex-col gap-y-10 md:flex-row mx-auto justify-between basis-full md:gap-8  px-6 py-10 xl:px-16 sm:py-20 border-l-none lg:border-l">
            <div className="flex flex-col gap-6 w-full">
              <h1 className="text-xl font-semibold text-[#4B4BB4]">Products</h1>
              <div className="grid grid-cols-2 gap-4 text-[#344045] w-[550px]">
                {productLinks.map((item, index) => {
                  return (
                    <Link
                      key={index}
                      href={item.href}
                      className="hover:underline transition-all"
                    >
                      {item.label}
                    </Link>
                  );
                })}
              </div>
            </div>

            <div className="flex flex-col gap-6 w-full">
              <h1 className="text-xl font-semibold text-[#4B4BB4]">Telex</h1>
              <div className="flex flex-col gap-4 text-[#344045]">
                {supportLinks.map((item, index) => {
                  return (
                    <Link
                      key={index}
                      href={item.href}
                      className="hover:underline transition-all"
                    >
                      {item.label}
                    </Link>
                  );
                })}
              </div>
            </div>

            <div className="flex flex-col gap-6 w-full">
              <h1 className="text-xl font-semibold text-[#4B4BB4]">
                Resources
              </h1>
              <div className="flex flex-col gap-4 text-[#344045]">
                {resourcesLinks.map((item, index) => {
                  return (
                    <Link
                      key={index}
                      href={item.href}
                      className="hover:underline transition-all"
                    >
                      {item.label}
                    </Link>
                  );
                })}
              </div>
            </div>
            {/* <div className="flex flex-col gap-6 w-full">
              <h1 className="text-xl font-semibold text-[#4B4BB4]">Legal</h1>
              <div className="flex flex-col gap-4 text-[#344045]">
                {legalLinks.map((item, index) => {
                  return (
                    <Link
                      key={index}
                      href={item.href}
                      className="hover:underline transition-all"
                    >
                      {item.label}
                    </Link>
                  );
                })}
              </div>
            </div> */}
          </div>
        </div>
      </div>
      <div className="w-full flex flex-col gap-6 lg:gap-0 lg:flex-row lg:justify-between items-center gap-y-5 bg-[#4848AD] ">
        <div className="w-full flex flex-col gap-3 lg:flex-row lg:justify-between items-center gap-y-5 bg-[#4848AD] px-6 py-2 sm:px-16 sm:py-8  max-w-[1550px] lg:mx-auto">
          <div className="flex gap-6">
            <Link
              href="https://www.instagram.com/telex.im/"
              className="hover:underline transition-all"
            >
              <Image src={instagramIcon} alt="instagram icon" />
            </Link>
            <Link
              href="https://www.tiktok.com/@telex.im"
              className="hover:underline transition-all"
            >
              <Image src={tiktokIcon} alt="tiktok icon" />
            </Link>
            <Link
              href="https://www.facebook.com/profile.php?id=61578751079130"
              className="hover:underline transition-all"
            >
              <Image src={facebookIcon} alt="facebook icon" />
            </Link>
            <Link
              href="https://x.com/teleximapp"
              className="hover:underline transition-all"
            >
              <Image
                src={"/images/twitter-white.svg"}
                width={24}
                height={24}
                alt="twitter icon"
              />
            </Link>
          </div>
          <div className="flex flex-col md:flex-row md:gap-x-5 justify-center items-center gap-y-5">
            <aside className="flex items-center gap-[6px]">
              {/* <Link
                href="/policy"
                className="text-sm font-medium hover:underline transition-all"
              >
                <p className="text-[14px]">Privacy Policy</p>
              </Link>

              <Link
                href="/terms-of-service"
                className="text-sm font-medium hover:underline transition-all"
              >
                <p className="text-[14px]">Terms of Use</p>
              </Link> */}
              <Link
                href="mailto:<EMAIL>"
                className="text-sm font-medium hover:underline transition-all"
              >
                <p className="text-[14px]"><EMAIL></p>
              </Link>
            </aside>
            <span>© 2025 Telex. All Rights Reserved</span>
            {/* <a href={version.release_notes_url}>
              <p>Version {version.number}</p>
            </a> */}
          </div>
        </div>
      </div>
    </footer>
  );
};
export default ExternalPageFooter;

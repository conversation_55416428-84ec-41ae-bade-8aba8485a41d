"use client";
import React from "react";
import { products } from "../../data/products";
import Image from "next/image";
import Link from "next/link";

const Monitoring = () => {
  return (
    <>
      <section className="py-16 px-2 md:px-6 pb-40">
        <p className="text-center mb-3 font-semibold">Products</p>
        <h2 className="text-3xl sm:text-4xl font-bold text-center mb-8 text-gray-900">
          From Sales to DevOps
        </h2>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl mx-auto">
          {products?.map((item, index) => {
            return (
              <Link href={item.link}>
                <div
                  key={index}
                  className="bg-white p-5 xl:p-8 border px-2 sm:px-4 rounded-xl shadow-xs mb-3 cursor-pointer overflow-hidden transition-all duration-300 hover:shadow-sm hover:border-black/20"
                >
                  <div
                    className="relative flex items-center px-4 py-5 rounded-xl h-20"
                    style={{ background: item.color }}
                  >
                    <h3 className="text-md font-semibold text-white mr-4 md:mr-10 xl:mr-20">
                      {item.title}
                    </h3>

                    <Image
                      src={item.background}
                      alt="background"
                      unoptimized
                      className="absolute size-20 top-3 -right-5 sm:right-0"
                    />
                  </div>

                  <p className="text-gray-600 my-4">{item.content}</p>
                  <span className="text-indigo-600 font-medium text-sm group-hover:underline">
                    Learn More &rarr;
                  </span>
                </div>
              </Link>
            );
          })}
        </div>
      </section>
    </>
  );
};

export default Monitoring;

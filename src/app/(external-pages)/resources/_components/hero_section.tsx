import React from "react";

const HeroSection = () => {
  return (
    <section className="relative flex items-center justify-center px-4 py-[120px]">
      <div
        className="absolute -z-10 top-0 left-0 w-full h-full opacity-10 bg-center bg-cover"
        style={{ backgroundImage: "url('/images/resources_hero_bg.jpeg')" }}
      />
      <div className="flex flex-col items-center gap-5">
        <h1 className="text-xl md:text-5xl font-semibold text-center xl:text-left">
          <span className=" text-[#6868F7]">The Telex Hub –</span> Learn to use Al in your company
        </h1>
        <p className="text-center text-[#344054] text-xs w-[300px] md:text-lg md:w-[490px]">
        We have here all the guides you need to know how to effectively use Al in your company.
        </p>
      </div>
      <div
        className="absolute bottom-0 left-0 w-full h-4 bg-center bg-cover"
        style={{ backgroundImage: "url('/images/resources_content_bg.jpeg')" }}
      />
    </section>
  );
};

export default HeroSection;

import React, { useState } from "react";
import {
  Accordion,
  AccordionItem,
  Accordion<PERSON>ontent,
  AccordionTrigger,
} from "~/components/ui/accordion";
import Image from "next/image";
import { CircleArrowDown, Minus, Plus } from "lucide-react";
import FAQComp from "~/telexComponents/homePageFaqs";

const faqData = [
  {
    id: 1,
    question: "What is Telex?",
    answer: "Telex is an Al-powered workflow automation platform that helps teams streamline tasks like content creation, invoice processing, lead generation, document management, and more, all from one place."
  },
  {
    id: 2,
    question: "How can I use Telex for my team?",
    answer: "You can organize workflows by creating dedicated channels for specific teams or use cases (e.g., marketing, devops, finance). Within each channel, you can activate Al agents tailored to your team's goals."
  },
  {
    id: 3,
    question: "Can I integrate Telex with my existing tools?",
    answer: "Yes. Telex integrates with tools like Google Docs, Slack, Gmail, Trello, Notion, GitHub, and more. These integrations enable Al agents to work within your current systems and simplify your operations."
  },
  {
    id: 4,
    question: "What tasks can Telex automate?",
    answer: "Telex can automate content creation, invoice processing, document analysis, data extraction, customer support, email marketing, lead nurturing, and more. Just choose a workflow, and activate an Al agent."
  },
  {
    id: 5,
    question: "Can I customize channels and agents?",
    answer: "Absolutely. You can personalize each Al agent to meet your business needs, adjust inputs, select the right tools, and define how the agent behaves in each workflow."
  }
];

const Faq: React.FC = () => {

  return (
    <div className="mx-auto max-w-[1240px] gap-[40px] flex flex-col md:flex-row justify-center items-stretch">
      <div className="w-full md:w-[50%] h-auto">
        <div className="hidden md:block rounded-lg overflow-hidden mx-auto w-full h-full sticky top-0">
          <Image
            src="/images/faq-image.jpg"
            alt="FAQ Image"
            width={1800}
            height={1800}
            className="object-cover w-full h-full"
            style={{ height: '100%', minHeight: '100%' }}
          />
        </div>
      </div>
      <div className="w-full flex items-start justify-center flex-col mx-6 md:w-[50%] sm:w-[500px] lg:w-[676px]">
        <h1 className="text-3xl font-semibold mb-4  w-full lg:w-auto lg:text-start">
          Got Questions?
        </h1>
        <p className="mb-8 lg:text-start w-full lg:w-auto">
          Explore common questions and answers to get the most out of Telex.
        </p>

        <div className="block mx-auto mb-2 md:hidden lg:hidden ">
          <div className="rounded-full overflow-hidden w-[250px] h-[250px]">
            <Image
              src="/images/faq-image.jpg"
              alt="FAQ Image"
              width={250}
              height={250}
              className="object-cover w-full h-full"
            />
          </div>
        </div>

        <div className="w-full text-left">
        <div className="flex justify-center w-full items-center mb-10">
            <Accordion
              type="single"
              collapsible
              className="w-full flex flex-col "
            >
              {faqData.map((item) => {
                return (
                  <AccordionItem
                    value={`item-${item.id}`}
                    className="mb-6 "
                    key={item.id}
                  >
                    <AccordionTrigger className="">
                      <p className="w-full leading-[150%] font-semibold text-[#101828] text-[14px] md:text-[16px] text-left">
                        {item.question}
                      </p>
                      <CircleArrowDown className="h-6 w-6 shrink-0 transition-transform duration-200 ml-1" />
                    </AccordionTrigger>
                    <AccordionContent className="mt-4 ">
                      <div
                        className="w-full lg:text-[16px] sm:text-[16px] xs:text-[14px] leading-relaxed text-[#344054]"
                        dangerouslySetInnerHTML={{ __html: item.answer }}
                      />
                    </AccordionContent>
                  </AccordionItem>
                );
              })}
            </Accordion>
          </div>
         
        </div>
      </div>
    </div>
  );
};

export default Faq;

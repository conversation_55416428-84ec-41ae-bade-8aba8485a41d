import React from "react";
import {
  Facebook,
  // Instagram,
  // Linkedin,
  Mail,
  MapPin,
  // Phone,
  Twitter,
} from "lucide-react";

import Link from "next/link";
import ContactForm from "./ContactForm";

const Contact = () => {
  return (
    <div className="w-full max-w-[55.8125rem] mx-auto flex flex-col items-center gap-14 lg:gap-[5.5rem] px-5 py-[5.5rem] lg:py-18">
      <div className="flex flex-col gap-3 text-center">
        <h1 className="text-4xl lg:text-[4.5rem] font-semibold leading-[2.925rem] lg:leading-[5.85rem] mt-16">
          Contact Our Team
        </h1>
        <p className="text-muted-foreground text-center max-w-3xl mx-auto lg:text-lg">
          We are here to help! Get in touch with the Telex team for support,
          feedback, or any questions you might have.
        </p>
      </div>

      <div className="w-full flex flex-col justify-start md:flex-row lg:align-baseline gap-16 lg:p-8">
        <ContactForm />

        <div className="flex flex-col gap-8">
          <p className="text-base font-semibold text-black leading-6">
            Business hours: 8am - 6pm
          </p>
          <div className="flex flex-col gap-6">
            <div className="flex gap-4 items-center">
              <Link
                href={
                  "https://www.bing.com/maps?osid=38a294ab-adea-46de-a874-301865223146&cp=51.56477~0.197639&lvl=16.26&pi=0&v=2&sV=2&form=S00027"
                }
                target="_blank"
                className="flex gap-4 items-center"
              >
                <div className="flex items-center justify-center bg-neutral-400 p-[0.38rem] rounded-[0.1875rem]">
                  <MapPin className="text-white" />
                </div>
                <p className="text-base font-normal leading-6 text-neutral-500">
                  Emerj LLC 30 N Gould St Ste R, Sheridan, Wyoming, 82801,
                  United States
                </p>
              </Link>
            </div>

            {/* <div className="flex gap-4 items-center">
              <Link
                href={"tel:+4403989898787"}
                className="flex gap-4 items-center"
                target="_blank"
              >
                <div className="flex items-center justify-center bg-neutral-400 p-[0.38rem] rounded-[0.1875rem]">
                  <Phone className="text-white" />
                </div>
                <p className="text-base font-normal leading-6 text-neutral-500">
                  +4403989898787
                </p>
              </Link>
            </div> */}

            <div className="flex gap-4 items-center">
              <Link
                href={"mailto:<EMAIL>"}
                className="flex gap-4 items-center"
              >
                <div className="flex items-center justify-center bg-neutral-400 p-[0.38rem] rounded-[0.1875rem]">
                  <Mail className="text-white" />
                </div>
                <p className="text-base font-normal leading-6 text-neutral-500">
                  Email: <EMAIL>
                </p>
              </Link>
            </div>

            <div className="flex gap-4 items-center">
              <Link
                href={"https://x.com/teleximapp"}
                target="_blank"
                className="flex gap-4 items-center"
              >
                <div className="flex items-center justify-center bg-neutral-400 p-[0.38rem] rounded-[0.1875rem]">
                  <Twitter className="text-white" />
                </div>
                <p className="text-base font-normal leading-6 text-neutral-500">
                  Twitter: @teleximapp
                </p>
              </Link>
            </div>

            <div className="flex gap-4 items-center">
              <Link
                href={"https://www.instagram.com/telexhq"}
                target="_blank"
                className="flex gap-4 items-center"
              >
                <div className="flex items-center justify-center bg-neutral-400 p-[0.38rem] rounded-[0.1875rem]">
                  <Facebook className="text-white" />
                </div>
                <p className="text-base font-normal leading-6 text-neutral-500">
                  Facebook: @Telex.im
                </p>
              </Link>
            </div>

            {/* <div className="flex gap-4 items-center">
              <Link
                className="flex gap-4 items-center"
                target="_blank"
                href={"https://www.linkedin.com/in/telexhq"}
              >
                <div className="flex items-center justify-center bg-neutral-400 p-[0.38rem] rounded-[0.1875rem]">
                  <Linkedin className="text-white" />
                </div>
                <p className="text-base font-normal leading-6 text-neutral-500">
                  Linkedin @telexhq
                </p>
              </Link>
            </div> */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Contact;
